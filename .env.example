# LOCAL DEVELOPMENT ENVIRONMENT CONFIGURATION
# This file contains environment variables for local Docker development
# Copy this to .env and modify values as needed

# Environment
NODE_ENV=development    # development, production
ENVIRONMENT=dev         # dev, staging, prod, local

# Server Configuration
SERVER_HOST=localhost
SERVER_TCP_PORT=3000
USE_SWAGGER=true

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=your_db_user
POSTGRES_PASSWORD=your_db_password
POSTGRES_DB=your_db_name

# AWS Configuration
AWS_REGION=your_aws_region
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_S3_BUCKET_NAME=your_s3_bucket_name
AWS_S3_REGION=your_aws_region

# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Other service configurations as needed
MAPBOX_ACCESS_TOKEN=your_mapbox_access_token
