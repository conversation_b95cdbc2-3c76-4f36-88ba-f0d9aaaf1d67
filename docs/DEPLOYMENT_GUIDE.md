# Comprehensive AWS Deployment Guide

This guide provides step-by-step instructions for deploying the ivent-api NestJS application to AWS using Terraform and GitHub Actions.

## Prerequisites

### Required Tools

- [AWS CLI v2](https://docs.aws.amazon.com/cli/latest/userguide/install-cliv2.html)
- [Terraform >= 1.0](https://www.terraform.io/downloads.html)
- [Docker](https://docs.docker.com/get-docker/)
- [Node.js 20+](https://nodejs.org/)
- [Git](https://git-scm.com/)

### AWS Account Setup

1. Create an AWS account if you don't have one
2. Create an IAM user with programmatic access
3. Attach the `AdministratorAccess` policy (for initial setup)
4. Download the access keys

### Local Environment Setup

```bash
# Install AWS CLI and configure
aws configure
# Enter your Access Key ID, Secret Access Key, Region (eu-central-1), and output format (json)

# Verify AWS configuration
aws sts get-caller-identity

# Install Terraform
# On macOS with Homebrew:
brew install terraform

# Verify Terraform installation
terraform --version
```

## Step-by-Step Deployment

### Step 1: Clone and Setup Repository

```bash
# Clone the repository
git clone <your-repo-url>
cd ivent_api

# Install dependencies
npm install

# Copy environment example
cp .env.example .env
# Edit .env with your configuration
```

### Step 2: Set Up AWS S3 Bucket for Terraform State

```bash
# Run the bucket setup script
./scripts/create-s3.sh dev
```

### Step 3: Deploy Infrastructure (Development)

```bash
# Navigate to terraform directory
cd terraform

# Deploy using the script
./scripts/deploy.sh dev plan
./scripts/deploy.sh dev apply
```

### Step 4: Build and Push Initial Docker Image

```bash
# Get ECR repository URL from Terraform output
ECR_REPO=$(terraform output -raw ecr_repository_url)

# Login to ECR
aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin $ECR_REPO

# Build and push image
docker build -t ivent-api:latest .
docker tag ivent-api:latest $ECR_REPO:latest
docker push $ECR_REPO:latest
```

### Step 5: Update ECS Service

```bash
# Force new deployment with the pushed image
aws ecs update-service \
  --cluster ivent-api-dev-cluster \
  --service ivent-api-dev-service \
  --force-new-deployment \
  --region eu-central-1

# Wait for deployment to complete
aws ecs wait services-stable \
  --cluster ivent-api-dev-cluster \
  --services ivent-api-dev-service \
  --region eu-central-1
```

### Step 6: Verify Deployment

```bash
# Get load balancer DNS name
ALB_DNS=$(terraform output -raw load_balancer_dns_name)

# Test health endpoint
curl http://$ALB_DNS/health

# Test API documentation (dev environment only)
curl http://$ALB_DNS/api
```

## Environment-Specific Deployments

### Development Environment

```bash
./scripts/deploy.sh dev apply
```

### Staging Environment

```bash
./scripts/deploy.sh staging apply
```

### Production Environment

```bash
./scripts/deploy.sh prod apply
```

## GitHub Actions CI/CD Setup

### Step 1: Configure GitHub Secrets

In your GitHub repository, go to Settings > Secrets and variables > Actions, and add:

**Required Secrets:**

- `AWS_ACCESS_KEY_ID`: Your AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret access key
- `DB_PASSWORD`: Database password
- `JWT_SECRET`: JWT secret key

**Optional Secrets:**

- `SNYK_TOKEN`: For security scanning
- `ALERT_EMAIL`: For CloudWatch alerts

### Step 2: Configure GitHub Environments

Create environments in GitHub (Settings > Environments):

- `development`
- `staging`
- `production`

Add environment protection rules for production:

- Required reviewers
- Wait timer
- Deployment branches (main only)

### Step 3: Trigger Deployments

- **Development**: Push to `develop` branch
- **Staging**: Push to `staging` branch or manual trigger
- **Production**: Push to `main` branch or create a release tag

## Local Development Setup

### Using Docker Compose

```bash
# Start local development environment
docker-compose up -d

# Setup local S3 (LocalStack)
./scripts/setup-local-s3.sh

# Run the application
npm run start:dev
```

### Environment Variables for Local Development

```bash
# .env file for local development
NODE_ENV=development
SERVER_HOST=localhost
SERVER_TCP_PORT=3000

# Local database (Docker Compose)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=ivent_user
POSTGRES_PASSWORD=dev_password
POSTGRES_DB=ivent_dev

# LocalStack S3
AWS_ENDPOINT_URL=http://localhost:4566
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_REGION=eu-central-1
AWS_S3_BUCKET_NAME=ivent-media-dev

# Other configurations
JWT_SECRET=dev-jwt-secret
USE_SWAGGER=true
```

## Testing

### Unit Tests

```bash
npm run test
```

### E2E Tests

```bash
npm run test:e2e
```

### Load Testing (Optional)

```bash
# Install artillery
npm install -g artillery

# Run load test
artillery quick --count 10 --num 100 http://your-alb-dns/health
```

## Monitoring and Maintenance

### CloudWatch Dashboard

Access your CloudWatch dashboard:

```bash
terraform output cloudwatch_dashboard_url
```

### Log Monitoring

```bash
# View ECS logs
aws logs describe-log-groups --log-group-name-prefix "/ecs/ivent-api"

# Tail logs
aws logs tail /ecs/ivent-api-dev --follow
```

### Database Maintenance

```bash
# Connect to RDS (requires bastion host or VPN)
psql -h your-rds-endpoint -U ivent_admin -d ivent_dev

# Create database backup
aws rds create-db-snapshot \
  --db-instance-identifier ivent-api-dev-db \
  --db-snapshot-identifier ivent-api-dev-backup-$(date +%Y%m%d)
```

## Troubleshooting

### Common Issues

**1. ECS Service Won't Start**

```bash
# Check service events
aws ecs describe-services \
  --cluster ivent-api-dev-cluster \
  --services ivent-api-dev-service \
  --query 'services[0].events'

# Check task definition
aws ecs describe-task-definition \
  --task-definition ivent-api-dev-app
```

**2. Database Connection Issues**

```bash
# Check security groups
aws ec2 describe-security-groups \
  --filters "Name=group-name,Values=*ivent-api-dev*"

# Test database connectivity from ECS task
aws ecs execute-command \
  --cluster ivent-api-dev-cluster \
  --task <task-id> \
  --container app \
  --interactive \
  --command "/bin/sh"
```

**3. Load Balancer Health Check Failures**

```bash
# Check target group health
aws elbv2 describe-target-health \
  --target-group-arn <target-group-arn>

# Check application logs
aws logs tail /ecs/ivent-api-dev --follow
```

### Rollback Procedures

**Manual Rollback:**

```bash
# List previous task definitions
aws ecs list-task-definitions \
  --family-prefix ivent-api-dev-app \
  --status ACTIVE

# Update service to previous task definition
aws ecs update-service \
  --cluster ivent-api-dev-cluster \
  --service ivent-api-dev-service \
  --task-definition ivent-api-dev-app:PREVIOUS_REVISION
```

**Infrastructure Rollback:**

```bash
# Revert to previous Terraform state
terraform plan -var-file="environments/dev/terraform.tfvars" -target=module.ecs
terraform apply -auto-approve
```

## Cleanup

### Destroy Environment

```bash
# Use the destroy workflow in GitHub Actions, or manually:
./scripts/deploy.sh dev destroy

# Confirm by typing "yes" when prompted
```

### Manual Cleanup

```bash
# Empty S3 buckets first
aws s3 rm s3://your-bucket-name --recursive

# Then run terraform destroy
terraform destroy -var-file="environments/dev/terraform.tfvars"
```

## Security Considerations

- Never commit sensitive data to version control
- Use AWS Systems Manager Parameter Store for secrets
- Regularly rotate database passwords and API keys
- Monitor CloudWatch alarms and respond to alerts
- Keep Docker base images updated
- Review and audit IAM permissions regularly

## Support and Resources

- [AWS Documentation](https://docs.aws.amazon.com/)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)
- [NestJS Documentation](https://docs.nestjs.com/)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)

For issues and questions, please refer to the project's issue tracker or contact the development team.
