#!/bin/bash

# Terraform Deployment Script
# Usage: scripts/terraform/deploy.sh <environment> [plan|apply|destroy]

# Source utility functions
# 1. Exit immediately if a command fails
# 2. Functions to print colored output
source "$(dirname "$0")/../utils.sh"

# Check if environment is provided
if [ -z "$1" ]; then
    print_error "Environment is required. Usage: scripts/terraform/deploy.sh <environment> [plan|apply|destroy]"
    print_error "Available environments: dev, staging, prod"
    exit 1
fi

ENVIRONMENT=$1
ACTION=${2:-plan}

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment. Must be one of: dev, staging, prod"
    exit 1
fi

# Validate action
if [[ ! "$ACTION" =~ ^(plan|apply|destroy)$ ]]; then
    print_error "Invalid action. Must be one of: plan, apply, destroy"
    exit 1
fi

print_status "Starting Terraform deployment for environment: $ENVIRONMENT"
print_status "Action: $ACTION"

# Set working directory to terraform root
cd "$(dirname "$0")/../../terraform"

# Check if required tools are installed and configured
check_terraform
check_aws
check_s3 

# Set backend configuration
BACKEND_KEY="terraform.tfstate"
BACKEND_REGION="eu-central-1"
BACKEND_BUCKET="ivent-terraform-state-${ENVIRONMENT}-${BACKEND_REGION}"

print_status "Using backend: s3://${BACKEND_BUCKET}/${BACKEND_KEY}"

check_s3 "$BACKEND_BUCKET"

# Initialize Terraform with backend configuration
print_status "Initializing Terraform..."
terraform init \
    -backend-config="bucket=${BACKEND_BUCKET}" \
    -backend-config="key=${BACKEND_KEY}" \
    -backend-config="region=${BACKEND_REGION}"

# Validate Terraform configuration
print_status "Validating Terraform configuration..."
terraform validate

# Check for required environment variables
if [ -z "$TF_VAR_db_password" ]; then
    print_warning "TF_VAR_db_password not set. Please add it into terraform/secrets.auto.tfvars before proceeding."
fi

if [ -z "$TF_VAR_jwt_secret" ]; then
    print_warning "TF_VAR_jwt_secret not set. Please add it into terraform/secrets.auto.tfvars before proceeding."
fi

# Set variables file
VAR_FILE="environments/${ENVIRONMENT}/terraform.tfvars"

if [ ! -f "$VAR_FILE" ]; then
    print_error "Variables file not found: $VAR_FILE"
    exit 1
fi

# Execute Terraform command
case $ACTION in
    plan)
        print_status "Running Terraform plan..."
        terraform plan -var-file="$VAR_FILE" -out="terraform-${ENVIRONMENT}.tfplan"
        print_status "Plan saved to terraform-${ENVIRONMENT}.tfplan"
        print_status "To apply this plan, run: scripts/terraform/deploy.sh $ENVIRONMENT apply"
        ;;
    apply)
        if [ -f "terraform-${ENVIRONMENT}.tfplan" ]; then
            print_status "Applying Terraform plan..."
            terraform apply "terraform-${ENVIRONMENT}.tfplan"
            rm -f "terraform-${ENVIRONMENT}.tfplan"
        else
            print_status "No plan file found. Running plan and apply..."
            terraform apply -var-file="$VAR_FILE" -auto-approve
        fi
        print_status "Deployment completed successfully!"

        # Run production database migrations manually
        if [ "$ENVIRONMENT" = "prod" ]; then
            print_status "Running database migrations..."
            # Ensure we run npm from the repository root (one level up from terraform/)
            cd ..
            if (npm run migration:run); then
                print_status "✅ Database migrations completed!"
            else
                print_error "❌ Database migrations failed!"
                print_warning "Application may not function correctly without migrations."
                print_warning "You can retry migrations with: npm run migration:run (from the repository root)"
            fi
            cd terraform
        fi

        # Show outputs
        print_status "Deployment outputs:"
        terraform output
        ;;
    destroy)
        print_warning "This will destroy all resources in the $ENVIRONMENT environment!"
        read -p "Are you sure you want to continue? (yes/no): " -r
        if [[ $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            print_status "Destroying infrastructure..."
            terraform destroy -var-file="$VAR_FILE" -auto-approve
            print_status "Infrastructure destroyed successfully!"
        else
            print_status "Destroy cancelled."
        fi
        ;;
esac

print_status "Script completed successfully!"
